'use client'

import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { useState, useEffect } from 'react'

interface TicketFiltersProps {
  profile: any
}

export function TicketFilters({ profile }: TicketFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [search, setSearch] = useState(searchParams.get('search') || '')

  const currentStatus = searchParams.get('status')
  const currentPriority = searchParams.get('priority')
  const currentAssigned = searchParams.get('assigned')

  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'open', label: 'Open' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'resolved', label: 'Resolved' },
    { value: 'closed', label: 'Closed' },
  ]

  const priorityOptions = [
    { value: '', label: 'All Priorities' },
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' },
  ]

  const assignedOptions = [
    { value: '', label: 'All Tickets' },
    ...(profile.role !== 'customer' ? [
      { value: 'me', label: 'Assigned to Me' },
      { value: 'unassigned', label: 'Unassigned' },
    ] : []),
  ]

  const updateFilter = (key: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (value) {
      params.set(key, value)
    } else {
      params.delete(key)
    }
    
    // Reset to first page when filtering
    params.delete('page')
    
    router.push(`?${params.toString()}`)
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    updateFilter('search', search)
  }

  const clearFilters = () => {
    setSearch('')
    router.push('/dashboard/tickets')
  }

  const hasActiveFilters = currentStatus || currentPriority || currentAssigned || search

  return (
    <Card>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Search */}
          <form onSubmit={handleSearch} className="flex space-x-2">
            <Input
              placeholder="Search tickets..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" variant="outline">
              Search
            </Button>
          </form>

          {/* Filters */}
          <div className="flex flex-wrap gap-2">
            {/* Status Filter */}
            <select
              value={currentStatus || ''}
              onChange={(e) => updateFilter('status', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* Priority Filter */}
            <select
              value={currentPriority || ''}
              onChange={(e) => updateFilter('priority', e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {priorityOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>

            {/* Assignment Filter (only for agents/admins) */}
            {profile.role !== 'customer' && (
              <select
                value={currentAssigned || ''}
                onChange={(e) => updateFilter('assigned', e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {assignedOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            )}

            {/* Clear Filters */}
            {hasActiveFilters && (
              <Button variant="outline" onClick={clearFilters}>
                Clear Filters
              </Button>
            )}
          </div>

          {/* Active Filters Display */}
          {hasActiveFilters && (
            <div className="flex flex-wrap gap-2">
              {currentStatus && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Status: {statusOptions.find(o => o.value === currentStatus)?.label}
                  <button
                    onClick={() => updateFilter('status', '')}
                    className="ml-1 text-blue-600 hover:text-blue-500"
                  >
                    ×
                  </button>
                </span>
              )}
              {currentPriority && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                  Priority: {priorityOptions.find(o => o.value === currentPriority)?.label}
                  <button
                    onClick={() => updateFilter('priority', '')}
                    className="ml-1 text-yellow-600 hover:text-yellow-500"
                  >
                    ×
                  </button>
                </span>
              )}
              {currentAssigned && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Assignment: {assignedOptions.find(o => o.value === currentAssigned)?.label}
                  <button
                    onClick={() => updateFilter('assigned', '')}
                    className="ml-1 text-green-600 hover:text-green-500"
                  >
                    ×
                  </button>
                </span>
              )}
              {search && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                  Search: "{search}"
                  <button
                    onClick={() => {
                      setSearch('')
                      updateFilter('search', '')
                    }}
                    className="ml-1 text-purple-600 hover:text-purple-500"
                  >
                    ×
                  </button>
                </span>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
