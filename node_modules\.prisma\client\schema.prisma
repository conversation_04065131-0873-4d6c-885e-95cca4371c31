// Ticketing System Database Schema
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Organization {
  id        String   @id @default(cuid())
  name      String
  domain    String?  @unique
  settings  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users           User[]
  tickets         Ticket[]
  projects        Project[]
  departments     Department[]
  auditUserLogs   UserAuditLog[]
  userPermissions UserPermission[]

  @@map("organizations")
}

model User {
  id          String    @id @default(cuid())
  email       String    @unique
  password    String
  fullName    String?
  role        Role      @default(CUSTOMER)
  isActive    Boolean   @default(true)
  lastLoginAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Foreign Keys
  organizationId String

  // Relations
  organization    Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdTickets  Ticket[]         @relation("TicketCreator")
  assignedTickets Ticket[]         @relation("TicketAssignee")
  ticketMessages  TicketMessage[]
  emailThreads    EmailThread[]
  userProjects    UserProject[]
  userDepartments UserDepartment[]
  auditActorLogs  UserAuditLog[]   @relation("UserAuditActor")
  auditTargetLogs UserAuditLog[]   @relation("UserAuditTarget")
  userPermissions UserPermission[]

  @@map("users")
}

model Ticket {
  id           String       @id @default(cuid())
  ticketNumber String       @unique
  title        String
  description  String?
  status       TicketStatus @default(OPEN)
  priority     Priority     @default(MEDIUM)
  category     String?
  tags         String?
  metadata     Json?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  resolvedAt   DateTime?

  // Foreign Keys
  organizationId String
  createdById    String
  assignedToId   String?
  projectId      String?
  departmentId   String?

  // Relations
  organization Organization    @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdBy    User            @relation("TicketCreator", fields: [createdById], references: [id])
  assignedTo   User?           @relation("TicketAssignee", fields: [assignedToId], references: [id])
  project      Project?        @relation(fields: [projectId], references: [id])
  department   Department?     @relation(fields: [departmentId], references: [id])
  messages     TicketMessage[]
  attachments  Attachment[]
  emailThreads EmailThread[]

  @@map("tickets")
}

model TicketMessage {
  id         String   @id @default(cuid())
  content    String
  isInternal Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Foreign Keys
  ticketId String
  authorId String

  // Relations
  ticket      Ticket       @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  author      User         @relation(fields: [authorId], references: [id])
  attachments Attachment[]

  @@map("ticket_messages")
}

model Attachment {
  id           String   @id @default(cuid())
  filename     String
  originalName String
  mimeType     String
  size         Int
  url          String
  createdAt    DateTime @default(now())

  // Foreign Keys
  ticketId  String?
  messageId String?

  // Relations
  ticket  Ticket?        @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  message TicketMessage? @relation(fields: [messageId], references: [id], onDelete: Cascade)

  @@map("attachments")
}

model EmailThread {
  id         String   @id @default(cuid())
  subject    String
  fromEmail  String
  toEmail    String
  messageId  String   @unique
  inReplyTo  String?
  references String?
  body       String
  isHtml     Boolean  @default(false)
  receivedAt DateTime @default(now())

  // Foreign Keys
  ticketId String?
  userId   String?

  // Relations
  ticket Ticket? @relation(fields: [ticketId], references: [id])
  user   User?   @relation(fields: [userId], references: [id])

  @@map("email_threads")
}

// Projects
model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // FKs
  organizationId String

  // Relations
  organization Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  users        UserProject[]
  tickets      Ticket[]

  @@map("projects")
}

model UserProject {
  userId    String
  projectId String

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@id([userId, projectId])
  @@map("user_projects")
}

// Departments
model Department {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // FKs
  organizationId String

  // Relations
  organization Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  users        UserDepartment[]
  tickets      Ticket[]

  @@map("departments")
}

model UserDepartment {
  userId       String
  departmentId String

  // Relations
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  department Department @relation(fields: [departmentId], references: [id], onDelete: Cascade)

  @@id([userId, departmentId])
  @@map("user_departments")
}

// Per-user permissions
model UserPermission {
  userId         String
  organizationId String
  permission     PermissionKey

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@id([userId, permission])
  @@map("user_permissions")
}

// User management audit logs
model UserAuditLog {
  id        String          @id @default(cuid())
  action    UserAuditAction
  before    Json?
  after     Json?
  ip        String?
  createdAt DateTime        @default(now())

  // FKs
  organizationId String
  actorId        String
  targetUserId   String

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  actor        User         @relation("UserAuditActor", fields: [actorId], references: [id])
  targetUser   User         @relation("UserAuditTarget", fields: [targetUserId], references: [id])

  @@map("user_audit_logs")
}

// Enums

// Enum for user management audit actions
enum UserAuditAction {
  USER_CREATE
  USER_ROLE_CHANGE
  USER_ACTIVATION
  USER_PROJECT_SET
  USER_DEPARTMENT_SET
  USER_PERMISSION_SET
}

enum Role {
  ADMIN
  MANAGER
  AGENT
  CUSTOMER
}

enum PermissionKey {
  DASHBOARD_VIEW
  TICKETS_VIEW
  TICKETS_UPDATE
  TICKETS_ASSIGN
  REPORTS_VIEW
  DEPARTMENTS_MANAGE
  PROJECTS_MANAGE
  AUDIT_VIEW
}

enum TicketStatus {
  OPEN
  IN_PROGRESS
  WAITING_FOR_CUSTOMER
  WAITING_FOR_AGENT
  RESOLVED
  CLOSED
  CANCELLED
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}
