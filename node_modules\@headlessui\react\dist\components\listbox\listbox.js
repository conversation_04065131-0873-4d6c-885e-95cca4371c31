"use client";import{useFocusRing as he}from"@react-aria/focus";import{useHover as Re}from"@react-aria/interactions";import C,{Fragment as fe,createContext as Te,use<PERSON>allback as Q,useContext as me,useEffect as De,useMemo as V,useRef as ne,useState as Ae}from"react";import{flushSync as $}from"react-dom";import{useActivePress as Se}from'../../hooks/use-active-press.js';import{useByComparator as _e}from'../../hooks/use-by-comparator.js';import{useControllable as Fe}from'../../hooks/use-controllable.js';import{useDefaultValue as Ce}from'../../hooks/use-default-value.js';import{useDidElementMove as Me}from'../../hooks/use-did-element-move.js';import{useDisposables as we}from'../../hooks/use-disposables.js';import{useElementSize as Be}from'../../hooks/use-element-size.js';import{useEvent as h}from'../../hooks/use-event.js';import{useId as re}from'../../hooks/use-id.js';import{useInertOthers as Ie}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as ue}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as ke}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ue}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ne}from'../../hooks/use-outside-click.js';import{useOwnerDocument as be}from'../../hooks/use-owner.js';import{Action as ae,useQuickRelease as He}from'../../hooks/use-quick-release.js';import{useResolveButtonType as Ge}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Ve}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as q}from'../../hooks/use-sync-refs.js';import{useTextValue as Ke}from'../../hooks/use-text-value.js';import{useTrackedPointer as ze}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as We,useTransition as Xe}from'../../hooks/use-transition.js';import{useDisabled as je}from'../../internal/disabled.js';import{FloatingProvider as Je,useFloatingPanel as Qe,useFloatingPanelProps as $e,useFloatingReference as qe,useFloatingReferenceProps as Ye,useResolvedAnchor as Ze}from'../../internal/floating.js';import{FormFields as et}from'../../internal/form-fields.js';import{useFrozenData as tt}from'../../internal/frozen.js';import{useProvidedId as ot}from'../../internal/id.js';import{OpenClosedProvider as nt,State as le,useOpenClosed as rt}from'../../internal/open-closed.js';import{stackMachines as at}from'../../machines/stack-machine.js';import{useSlice as M}from'../../react-glue.js';import{isDisabledReactIssue7711 as lt}from'../../utils/bugs.js';import{Focus as R}from'../../utils/calculate-active-index.js';import{disposables as it}from'../../utils/disposables.js';import*as st from'../../utils/dom.js';import{Focus as ye,FocusableMode as pt,focusFrom as ut,isFocusableElement as dt}from'../../utils/focus-management.js';import{attemptSubmit as ct}from'../../utils/form.js';import{match as ie}from'../../utils/match.js';import{getOwnerDocument as ft}from'../../utils/owner.js';import{RenderFeatures as xe,forwardRefWithAs as Y,mergeProps as Oe,useRender as Z}from'../../utils/render.js';import{useDescribedBy as Tt}from'../description/description.js';import{Keys as c}from'../keyboard.js';import{Label as mt,useLabelledBy as bt,useLabels as yt}from'../label/label.js';import{MouseButton as xt}from'../mouse.js';import{Portal as Ot}from'../portal/portal.js';import{ActionTypes as Lt,ActivationTrigger as de,ListboxStates as f,ValueMode as U}from'./listbox-machine.js';import{ListboxContext as Pt,useListboxMachine as gt,useListboxMachineContext as ce}from'./listbox-machine-glue.js';let se=Te(null);se.displayName="ListboxDataContext";function ee(P){let D=me(se);if(D===null){let O=new Error(`<${P} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(O,ee),O}return D}let vt=fe;function Et(P,D){let O=re(),u=je(),{value:l,defaultValue:p,form:_,name:i,onChange:T,by:n,invalid:m=!1,disabled:b=u||!1,horizontal:a=!1,multiple:t=!1,__demoMode:s=!1,...A}=P;const g=a?"horizontal":"vertical";let N=q(D),F=Ce(p),[d=t?[]:void 0,L]=Fe(l,T,F),y=gt({id:O,__demoMode:s}),B=ne({static:!1,hold:!1}),H=ne(new Map),w=_e(n),I=Q(E=>ie(r.mode,{[U.Multi]:()=>d.some(j=>w(j,E)),[U.Single]:()=>w(d,E)}),[d]),r=V(()=>({value:d,disabled:b,invalid:m,mode:t?U.Multi:U.Single,orientation:g,onChange:L,compare:w,isSelected:I,optionsPropsRef:B,listRef:H}),[d,b,m,t,g,L,w,I,B,H]);ue(()=>{y.state.dataRef.current=r},[r]);let x=M(y,E=>E.listboxState),G=at.get(null),K=M(G,Q(E=>G.selectors.isTop(E,O),[G,O])),[v,z]=M(y,E=>[E.buttonElement,E.optionsElement]);Ne(K,[v,z],(E,j)=>{y.send({type:Lt.CloseListbox}),dt(j,pt.Loose)||(E.preventDefault(),v==null||v.focus())});let W=V(()=>({open:x===f.Open,disabled:b,invalid:m,value:d}),[x,b,m,d]),[X,te]=yt({inherit:!0}),o={ref:N},k=Q(()=>{if(F!==void 0)return L==null?void 0:L(F)},[L,F]),oe=Z();return C.createElement(te,{value:X,props:{htmlFor:v==null?void 0:v.id},slot:{open:x===f.Open,disabled:b}},C.createElement(Je,null,C.createElement(Pt.Provider,{value:y},C.createElement(se.Provider,{value:r},C.createElement(nt,{value:ie(x,{[f.Open]:le.Open,[f.Closed]:le.Closed})},i!=null&&d!=null&&C.createElement(et,{disabled:b,data:{[i]:d},form:_,onReset:k}),oe({ourProps:o,theirProps:A,slot:W,defaultTag:vt,name:"Listbox"}))))))}let ht="button";function Rt(P,D){let O=re(),u=ot(),l=ee("Listbox.Button"),p=ce("Listbox.Button"),{id:_=u||`headlessui-listbox-button-${O}`,disabled:i=l.disabled||!1,autoFocus:T=!1,...n}=P,m=q(D,qe(),p.actions.setButtonElement),b=Ye(),[a,t,s]=M(p,o=>[o.listboxState,o.buttonElement,o.optionsElement]),A=a===f.Open;He(A,{trigger:t,action:Q(o=>{if(t!=null&&t.contains(o.target))return ae.Ignore;let k=o.target.closest('[role="option"]:not([data-disabled])');return st.isHTMLElement(k)?ae.Select(k):s!=null&&s.contains(o.target)?ae.Ignore:ae.Close},[t,s]),close:p.actions.closeListbox,select:p.actions.selectActiveOption});let g=h(o=>{switch(o.key){case c.Enter:ct(o.currentTarget);break;case c.Space:case c.ArrowDown:o.preventDefault(),p.actions.openListbox({focus:l.value?R.Nothing:R.First});break;case c.ArrowUp:o.preventDefault(),p.actions.openListbox({focus:l.value?R.Nothing:R.Last});break}}),N=h(o=>{switch(o.key){case c.Space:o.preventDefault();break}}),F=h(o=>{var k;if(o.button===xt.Left){if(lt(o.currentTarget))return o.preventDefault();p.state.listboxState===f.Open?($(()=>p.actions.closeListbox()),(k=p.state.buttonElement)==null||k.focus({preventScroll:!0})):(o.preventDefault(),p.actions.openListbox({focus:R.Nothing}))}}),d=ne(null),L=h(o=>{d.current=o.pointerType,o.pointerType==="mouse"&&F(o)}),y=h(o=>{d.current!=="mouse"&&F(o)}),B=h(o=>o.preventDefault()),H=bt([_]),w=Tt(),{isFocusVisible:I,focusProps:r}=he({autoFocus:T}),{isHovered:x,hoverProps:G}=Re({isDisabled:i}),{pressed:K,pressProps:v}=Se({disabled:i}),z=V(()=>({open:a===f.Open,active:K||a===f.Open,disabled:i,invalid:l.invalid,value:l.value,hover:x,focus:I,autofocus:T}),[a,l.value,i,x,I,K,l.invalid,T]),W=M(p,o=>o.listboxState===f.Open),X=Oe(b(),{ref:m,id:_,type:Ge(P,t),"aria-haspopup":"listbox","aria-controls":s==null?void 0:s.id,"aria-expanded":W,"aria-labelledby":H,"aria-describedby":w,disabled:i||void 0,autoFocus:T,onKeyDown:g,onKeyUp:N,onKeyPress:B,onPointerDown:L,onClick:y},r,G,v);return Z()({ourProps:X,theirProps:n,slot:z,defaultTag:ht,name:"Listbox.Button"})}let Le=Te(!1),Dt="div",At=xe.RenderStrategy|xe.Static;function St(P,D){let O=re(),{id:u=`headlessui-listbox-options-${O}`,anchor:l,portal:p=!1,modal:_=!0,transition:i=!1,...T}=P,n=Ze(l),[m,b]=Ae(null);n&&(p=!0);let a=ee("Listbox.Options"),t=ce("Listbox.Options"),[s,A,g,N]=M(t,e=>[e.listboxState,e.buttonElement,e.optionsElement,e.__demoMode]),F=be(A),d=be(g),L=rt(),[y,B]=Xe(i,m,L!==null?(L&le.Open)===le.Open:s===f.Open);Ue(y,A,t.actions.closeListbox);let H=N?!1:_&&s===f.Open;Ve(H,d);let w=N?!1:_&&s===f.Open;Ie(w,{allowed:Q(()=>[A,g],[A,g])});let I=s!==f.Open,x=Me(I,A)?!1:y,G=y&&s===f.Closed,K=tt(G,a.value),v=h(e=>a.compare(K,e)),z=M(t,e=>{var J;if(n==null||!((J=n==null?void 0:n.to)!=null&&J.includes("selection")))return null;let S=e.options.findIndex(pe=>v(pe.dataRef.current.value));return S===-1&&(S=0),S}),W=(()=>{if(n==null)return;if(z===null)return{...n,inner:void 0};let e=Array.from(a.listRef.current.values());return{...n,inner:{listRef:{current:e},index:z}}})(),[X,te]=Qe(W),o=$e(),k=q(D,n?X:null,t.actions.setOptionsElement,b),oe=we();De(()=>{var S;let e=g;e&&s===f.Open&&e!==((S=ft(e))==null?void 0:S.activeElement)&&(e==null||e.focus({preventScroll:!0}))},[s,g]);let E=h(e=>{var S,J;switch(oe.dispose(),e.key){case c.Space:if(t.state.searchQuery!=="")return e.preventDefault(),e.stopPropagation(),t.actions.search(e.key);case c.Enter:if(e.preventDefault(),e.stopPropagation(),t.state.activeOptionIndex!==null){let{dataRef:pe}=t.state.options[t.state.activeOptionIndex];t.actions.onChange(pe.current.value)}a.mode===U.Single&&($(()=>t.actions.closeListbox()),(S=t.state.buttonElement)==null||S.focus({preventScroll:!0}));break;case ie(a.orientation,{vertical:c.ArrowDown,horizontal:c.ArrowRight}):return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:R.Next});case ie(a.orientation,{vertical:c.ArrowUp,horizontal:c.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:R.Previous});case c.Home:case c.PageUp:return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:R.First});case c.End:case c.PageDown:return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:R.Last});case c.Escape:e.preventDefault(),e.stopPropagation(),$(()=>t.actions.closeListbox()),(J=t.state.buttonElement)==null||J.focus({preventScroll:!0});return;case c.Tab:e.preventDefault(),e.stopPropagation(),$(()=>t.actions.closeListbox()),ut(t.state.buttonElement,e.shiftKey?ye.Previous:ye.Next);break;default:e.key.length===1&&(t.actions.search(e.key),oe.setTimeout(()=>t.actions.clearSearch(),350));break}}),j=M(t,e=>{var S;return(S=e.buttonElement)==null?void 0:S.id}),Pe=V(()=>({open:s===f.Open}),[s]),ge=Oe(n?o():{},{id:u,ref:k,"aria-activedescendant":M(t,t.selectors.activeDescendantId),"aria-multiselectable":a.mode===U.Multi?!0:void 0,"aria-labelledby":j,"aria-orientation":a.orientation,onKeyDown:E,role:"listbox",tabIndex:s===f.Open?0:void 0,style:{...T.style,...te,"--button-width":Be(A,!0).width},...We(B)}),ve=Z(),Ee=V(()=>a.mode===U.Multi?a:{...a,isSelected:v},[a,v]);return C.createElement(Ot,{enabled:p?P.static||y:!1,ownerDocument:F},C.createElement(se.Provider,{value:Ee},ve({ourProps:ge,theirProps:T,slot:Pe,defaultTag:Dt,features:At,visible:x,name:"Listbox.Options"})))}let _t="div";function Ft(P,D){let O=re(),{id:u=`headlessui-listbox-option-${O}`,disabled:l=!1,value:p,..._}=P,i=me(Le)===!0,T=ee("Listbox.Option"),n=ce("Listbox.Option"),m=M(n,r=>n.selectors.isActive(r,u)),b=T.isSelected(p),a=ne(null),t=Ke(a),s=ke({disabled:l,value:p,domRef:a,get textValue(){return t()}}),A=q(D,a,r=>{r?T.listRef.current.set(u,r):T.listRef.current.delete(u)}),g=M(n,r=>n.selectors.shouldScrollIntoView(r,u));ue(()=>{if(g)return it().requestAnimationFrame(()=>{var r,x;(x=(r=a.current)==null?void 0:r.scrollIntoView)==null||x.call(r,{block:"nearest"})})},[g,a]),ue(()=>{if(!i)return n.actions.registerOption(u,s),()=>n.actions.unregisterOption(u)},[s,u,i]);let N=h(r=>{var x;if(l)return r.preventDefault();n.actions.onChange(p),T.mode===U.Single&&($(()=>n.actions.closeListbox()),(x=n.state.buttonElement)==null||x.focus({preventScroll:!0}))}),F=h(()=>{if(l)return n.actions.goToOption({focus:R.Nothing});n.actions.goToOption({focus:R.Specific,id:u})}),d=ze(),L=h(r=>d.update(r)),y=h(r=>{d.wasMoved(r)&&(l||m&&n.state.activationTrigger===de.Pointer||n.actions.goToOption({focus:R.Specific,id:u},de.Pointer))}),B=h(r=>{d.wasMoved(r)&&(l||m&&n.state.activationTrigger===de.Pointer&&n.actions.goToOption({focus:R.Nothing}))}),H=V(()=>({active:m,focus:m,selected:b,disabled:l,selectedOption:b&&i}),[m,b,l,i]),w=i?{}:{id:u,ref:A,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":b,disabled:void 0,onClick:N,onFocus:F,onPointerEnter:L,onMouseEnter:L,onPointerMove:y,onMouseMove:y,onPointerLeave:B,onMouseLeave:B},I=Z();return!b&&i?null:I({ourProps:w,theirProps:_,slot:H,defaultTag:_t,name:"Listbox.Option"})}let Ct=fe;function Mt(P,D){let{options:O,placeholder:u,...l}=P,_={ref:q(D)},i=ee("ListboxSelectedOption"),T=V(()=>({}),[]),n=i.value===void 0||i.value===null||i.mode===U.Multi&&Array.isArray(i.value)&&i.value.length===0,m=Z();return C.createElement(Le.Provider,{value:!0},m({ourProps:_,theirProps:{...l,children:C.createElement(C.Fragment,null,u&&n?u:O)},slot:T,defaultTag:Ct,name:"ListboxSelectedOption"}))}let wt=Y(Et),Bt=Y(Rt),It=mt,kt=Y(St),Ut=Y(Ft),Nt=Y(Mt),Io=Object.assign(wt,{Button:Bt,Label:It,Options:kt,Option:Ut,SelectedOption:Nt});export{Io as Listbox,Bt as ListboxButton,It as ListboxLabel,Ut as ListboxOption,kt as ListboxOptions,Nt as ListboxSelectedOption};
