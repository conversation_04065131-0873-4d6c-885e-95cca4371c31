'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  HomeIcon,
  TicketIcon,
  UsersIcon,
  ChartBarIcon,
  CogIcon,
  PlusIcon,
} from '@heroicons/react/24/outline'

interface SidebarProps {
  user?: { fullName?: string | null; email: string; role: string }
  profile?: any // legacy shape
}

export function Sidebar({ user, profile }: SidebarProps) {
  const pathname = usePathname()

  const role = (user?.role || profile?.role || '').toString().toLowerCase()
  const displayName = user?.fullName || profile?.full_name || user?.email || profile?.email || 'User'

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
    { name: 'Tickets', href: '/dashboard/tickets', icon: TicketIcon },
    { name: 'New Ticket', href: '/dashboard/tickets/new', icon: PlusIcon },
  ]

  if (role === 'admin' || role === 'agent' || role === 'manager') {
    navigation.push(
      { name: 'Reports', href: '/dashboard/reports', icon: ChartBarIcon },
    )
  }
  if (role === 'admin') {
    navigation.push({ name: 'Users', href: '/dashboard/users', icon: UsersIcon })
  }
  if (role === 'admin') {
    navigation.push({ name: 'Departments', href: '/dashboard/departments', icon: CogIcon })
    navigation.push({ name: 'Email configuration', href: '/dashboard/email-config', icon: CogIcon })
  }

  return (
    <aside className="hidden md:flex md:w-64 md:flex-col">
      <div className="flex flex-col flex-grow pt-5 bg-slate-900 text-slate-200 border-r border-slate-800 overflow-y-auto">
        <div className="flex items-center flex-shrink-0 px-4">
          <h1 className="text-xl font-semibold text-white">WeCare</h1>
        </div>
        <div className="mt-6 flex-grow flex flex-col">
          <nav className="flex-1 px-2 space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    isActive
                      ? 'bg-slate-800 border-blue-500 text-white'
                      : 'border-transparent text-slate-300 hover:bg-slate-800 hover:text-white',
                    'group flex items-center px-2 py-2 text-sm font-medium border-l-4 rounded-r-md'
                  )}
                >
                  <item.icon
                    className={cn(
                      isActive ? 'text-blue-400' : 'text-slate-400 group-hover:text-slate-300',
                      'mr-3 h-5 w-5'
                    )}
                    aria-hidden="true"
                  />
                  {item.name}
                </Link>
              )}
            )}
          </nav>
        </div>
        <div className="flex-shrink-0 p-4 border-t border-slate-800">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
                <span className="text-sm font-medium text-white">
                  {(displayName || '').charAt(0) || 'U'}
                </span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-slate-100">{displayName}</p>
              {role && <p className="text-xs text-slate-400 capitalize">{role}</p>}
            </div>
          </div>
        </div>
      </div>
    </aside>
  )
}
